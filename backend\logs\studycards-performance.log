2025-07-22 21:40:42.535 [MessageBroker-13] ERROR PERFORMANCE - 
                [hourlyPerformanceReport] [0ms] [] FAILED_SERVICE_METHOD: StatisticsMonitoringService.getMonitoringStats failed after 0ms with error: NullPointerException
2025-07-22 21:40:42.932 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [267ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 267ms
2025-07-22 21:40:42.932 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [267ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 267ms
2025-07-22 21:40:42.932 [MessageBroker-2] INFO  PERFORMANCE - 
                [sendVerificationReminders] [319ms] [] DB_OPERATION: $Proxy194.findUnverifiedUsersInTimeR<PERSON><PERSON> took 319ms
2025-07-22 21:40:42.932 [MessageBroker-15] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [319ms] [] DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 319ms
2025-07-22 21:40:42.943 [main] INFO  PERFORMANCE - 
                [] [178ms] [] DB_OPERATION: $Proxy211.count took 178ms
2025-07-22 21:40:43.120 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [101ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 101ms
2025-07-22 21:40:43.120 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [105ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 105ms
2025-07-22 21:40:43.225 [MessageBroker-14] INFO  PERFORMANCE - 
                [periodicHealthCheck] [582ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 582ms
2025-07-22 21:40:43.225 [MessageBroker-12] INFO  PERFORMANCE - 
                [cacheWarming] [596ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 596ms
2025-07-22 21:40:43.225 [MessageBroker-14] INFO  PERFORMANCE - 
                [periodicHealthCheck] [690ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 690ms
2025-07-22 21:40:43.225 [MessageBroker-12] INFO  PERFORMANCE - 
                [cacheWarming] [690ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 690ms
2025-07-22 22:53:11.840 [MessageBroker-13] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [441ms] [] DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 441ms
2025-07-22 22:53:11.953 [ForkJoinPool.commonPool-worker-8] INFO  PERFORMANCE - 
                [] [201ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 201ms
2025-07-22 22:53:12.011 [MessageBroker-13] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [626ms] [] SERVICE_METHOD: SubscriptionSchedulerService.updateExpiredSubscriptions took 626ms
2025-07-22 22:53:12.161 [ForkJoinPool.commonPool-worker-8] INFO  PERFORMANCE - 
                [] [208ms] [] DB_OPERATION: $Proxy194.countActiveUsersSince took 208ms
2025-07-22 22:53:12.462 [ForkJoinPool.commonPool-worker-8] INFO  PERFORMANCE - 
                [] [301ms] [] DB_OPERATION: $Proxy194.countUsersWithStudyDataSince took 301ms
2025-07-22 22:53:12.720 [ForkJoinPool.commonPool-worker-8] INFO  PERFORMANCE - 
                [] [258ms] [] DB_OPERATION: $Proxy209.countUsersWithImprovement took 258ms
2025-07-22 22:53:13.331 [ForkJoinPool.commonPool-worker-8] WARN  PERFORMANCE - 
                [] [609ms] [] SLOW_DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 609ms
2025-07-22 22:53:13.648 [ForkJoinPool.commonPool-worker-8] INFO  PERFORMANCE - 
                [] [315ms] [] DB_OPERATION: $Proxy209.getTotalCardsStudied took 315ms
2025-07-22 22:53:14.614 [ForkJoinPool.commonPool-worker-8] WARN  PERFORMANCE - 
                [] [691ms] [] SLOW_DB_OPERATION: $Proxy207.countByDeckDeletedFalse took 691ms
2025-07-22 22:53:14.845 [ForkJoinPool.commonPool-worker-8] INFO  PERFORMANCE - 
                [] [231ms] [] DB_OPERATION: $Proxy208.countDistinctTags took 231ms
2025-07-22 22:53:14.923 [MessageBroker-4] WARN  PERFORMANCE - 
                [periodicHealthCheck] [3524ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 3524ms
2025-07-22 22:53:14.936 [MessageBroker-4] WARN  PERFORMANCE - 
                [periodicHealthCheck] [3551ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 3551ms
2025-07-22 22:53:15.310 [ForkJoinPool.commonPool-worker-8] INFO  PERFORMANCE - 
                [] [163ms] [] DB_OPERATION: $Proxy209.countUsersWithImprovement took 163ms
2025-07-22 22:53:15.752 [ForkJoinPool.commonPool-worker-8] INFO  PERFORMANCE - 
                [] [394ms] [] DB_OPERATION: $Proxy209.getAverageStudyStreakDays took 394ms
2025-07-22 22:53:16.056 [ForkJoinPool.commonPool-worker-8] INFO  PERFORMANCE - 
                [] [128ms] [] DB_OPERATION: $Proxy206.countByPublicTrueAndDeletedFalse took 128ms
2025-07-22 22:53:16.083 [MessageBroker-4] WARN  PERFORMANCE - 
                [periodicHealthCheck] [1143ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 1143ms
2025-07-22 22:53:16.086 [MessageBroker-4] WARN  PERFORMANCE - 
                [periodicHealthCheck] [1150ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 1150ms
2025-07-22 22:53:16.244 [ForkJoinPool.commonPool-worker-8] INFO  PERFORMANCE - 
                [] [139ms] [] DB_OPERATION: $Proxy194.countActiveUsersSince took 139ms
2025-07-22 22:53:16.583 [ForkJoinPool.commonPool-worker-8] INFO  PERFORMANCE - 
                [] [199ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 199ms
2025-07-22 22:53:17.017 [MessageBroker-4] INFO  PERFORMANCE - 
                [periodicHealthCheck] [927ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 927ms
2025-07-22 22:53:17.022 [MessageBroker-4] INFO  PERFORMANCE - 
                [periodicHealthCheck] [936ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 936ms
2025-07-22 22:53:17.411 [ForkJoinPool.commonPool-worker-8] INFO  PERFORMANCE - 
                [] [140ms] [] DB_OPERATION: $Proxy194.countUsersWithStudyDataSince took 140ms
2025-07-22 22:53:17.825 [MessageBroker-4] INFO  PERFORMANCE - 
                [periodicHealthCheck] [729ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 729ms
2025-07-22 22:53:17.855 [MessageBroker-4] INFO  PERFORMANCE - 
                [periodicHealthCheck] [793ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 793ms
2025-07-22 23:02:49.706 [MessageBroker-14] ERROR PERFORMANCE - 
                [hourlyPerformanceReport] [8ms] [] FAILED_SERVICE_METHOD: StatisticsMonitoringService.getMonitoringStats failed after 8ms with error: NullPointerException
2025-07-22 23:02:50.175 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [291ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 291ms
2025-07-22 23:02:50.175 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [294ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 294ms
2025-07-22 23:02:50.175 [MessageBroker-16] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [359ms] [] DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 359ms
2025-07-22 23:02:50.175 [MessageBroker-13] INFO  PERFORMANCE - 
                [sendVerificationReminders] [359ms] [] DB_OPERATION: $Proxy194.findUnverifiedUsersInTimeRange took 359ms
2025-07-22 23:02:50.181 [main] INFO  PERFORMANCE - 
                [] [235ms] [] DB_OPERATION: $Proxy211.count took 235ms
2025-07-22 23:02:50.403 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [137ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 137ms
2025-07-22 23:02:50.403 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [121ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 121ms
2025-07-22 23:02:50.518 [MessageBroker-4] INFO  PERFORMANCE - 
                [periodicHealthCheck] [659ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 659ms
2025-07-22 23:02:50.518 [MessageBroker-3] INFO  PERFORMANCE - 
                [cacheWarming] [659ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 659ms
2025-07-22 23:02:50.521 [MessageBroker-4] INFO  PERFORMANCE - 
                [periodicHealthCheck] [823ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 823ms
2025-07-22 23:02:50.521 [MessageBroker-3] INFO  PERFORMANCE - 
                [cacheWarming] [823ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 823ms
2025-07-22 23:07:58.876 [http-nio-8082-exec-3] INFO  PERFORMANCE - 
                [getStudyActivityData] [139ms] [] DB_OPERATION: $Proxy209.findByUserAndDateRange took 139ms
2025-07-22 23:07:58.884 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [getUserStudySessions] [127ms] [] DB_OPERATION: $Proxy209.findByUserWithDeckFetch took 127ms
2025-07-22 23:07:58.915 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [getUserDecks] [105ms] [] DB_OPERATION: $Proxy206.findByCreatorWithFilters took 105ms
2025-07-22 23:07:58.954 [http-nio-8082-exec-8] INFO  PERFORMANCE - 
                [getEnhancedPopularDecks] [117ms] [] DB_OPERATION: $Proxy206.findEnhancedPopularPublicDecks took 117ms
2025-07-22 23:07:59.041 [http-nio-8082-exec-1] ERROR PERFORMANCE - 
                [getDashboardData] [193ms] [] FAILED_DB_OPERATION: $Proxy209.getDistinctStudyDatesByUser failed after 193ms with error: InvalidDataAccessResourceUsageException
2025-07-22 23:07:59.124 [http-nio-8082-exec-4] INFO  PERFORMANCE - 
                [getCardsForReview] [122ms] [] DB_OPERATION: $Proxy207.findAllDueCardsForUser took 122ms
2025-07-22 23:07:59.188 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getCardsForReview] [209ms] [] FAILED_SERVICE_METHOD: DashboardService.getCardsForReview failed after 209ms with error: LazyInitializationException
2025-07-22 23:07:59.193 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [] [214ms] [] FAILED_ENDPOINT: DashboardController.getCardsForReview failed after 214ms with error: LazyInitializationException
2025-07-22 23:07:59.403 [StudyCards-Async-2] INFO  PERFORMANCE - 
                [createDueCardsReminder] [180ms] [] DB_OPERATION: $Proxy220.save took 180ms
2025-07-22 23:07:59.452 [http-nio-8082-exec-1] INFO  PERFORMANCE - 
                [getDashboardData] [672ms] [] SERVICE_METHOD: DashboardService.getDashboardData took 672ms
2025-07-22 23:08:00.268 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [getCardsForReview] [11ms] [] FAILED_SERVICE_METHOD: DashboardService.getCardsForReview failed after 11ms with error: LazyInitializationException
2025-07-22 23:08:00.269 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [] [12ms] [] FAILED_ENDPOINT: DashboardController.getCardsForReview failed after 12ms with error: LazyInitializationException
2025-07-22 23:08:02.316 [http-nio-8082-exec-6] ERROR PERFORMANCE - 
                [getCardsForReview] [12ms] [] FAILED_SERVICE_METHOD: DashboardService.getCardsForReview failed after 12ms with error: LazyInitializationException
2025-07-22 23:08:02.317 [http-nio-8082-exec-6] ERROR PERFORMANCE - 
                [] [13ms] [] FAILED_ENDPOINT: DashboardController.getCardsForReview failed after 13ms with error: LazyInitializationException
2025-07-22 23:09:44.237 [http-nio-8082-exec-10] ERROR PERFORMANCE - 
                [getDashboardData] [15ms] [] FAILED_DB_OPERATION: $Proxy209.getDistinctStudyDatesByUser failed after 15ms with error: InvalidDataAccessResourceUsageException
2025-07-22 23:09:44.268 [http-nio-8082-exec-3] ERROR PERFORMANCE - 
                [getCardsForReview] [87ms] [] FAILED_SERVICE_METHOD: DashboardService.getCardsForReview failed after 87ms with error: LazyInitializationException
2025-07-22 23:09:44.269 [http-nio-8082-exec-3] ERROR PERFORMANCE - 
                [] [88ms] [] FAILED_ENDPOINT: DashboardController.getCardsForReview failed after 88ms with error: LazyInitializationException
2025-07-22 23:09:44.459 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [getDashboardData] [120ms] [] DB_OPERATION: $Proxy209.findRecentlyStudiedDecksForUser took 120ms
2025-07-22 23:09:45.386 [http-nio-8082-exec-7] ERROR PERFORMANCE - 
                [getCardsForReview] [29ms] [] FAILED_SERVICE_METHOD: DashboardService.getCardsForReview failed after 29ms with error: LazyInitializationException
2025-07-22 23:09:45.387 [http-nio-8082-exec-7] ERROR PERFORMANCE - 
                [] [30ms] [] FAILED_ENDPOINT: DashboardController.getCardsForReview failed after 30ms with error: LazyInitializationException
2025-07-22 23:09:47.435 [http-nio-8082-exec-1] ERROR PERFORMANCE - 
                [getCardsForReview] [11ms] [] FAILED_SERVICE_METHOD: DashboardService.getCardsForReview failed after 11ms with error: LazyInitializationException
2025-07-22 23:09:47.435 [http-nio-8082-exec-1] ERROR PERFORMANCE - 
                [] [11ms] [] FAILED_ENDPOINT: DashboardController.getCardsForReview failed after 11ms with error: LazyInitializationException
2025-07-22 23:09:59.511 [http-nio-8082-exec-3] ERROR PERFORMANCE - 
                [getPersonalizedRecommendations] [21ms] [] FAILED_DB_OPERATION: DeckRepositoryCustomImpl.findUserFavoriteTagsUsingDeckTags failed after 21ms with error: DataAccessException
2025-07-22 23:09:59.517 [http-nio-8082-exec-3] ERROR PERFORMANCE - 
                [getPersonalizedRecommendations] [30ms] [] FAILED_DB_OPERATION: $Proxy206.findUserFavoriteTags failed after 30ms with error: DataAccessException
2025-07-22 23:09:59.518 [http-nio-8082-exec-3] ERROR PERFORMANCE - 
                [getPersonalizedRecommendations] [85ms] [] FAILED_SERVICE_METHOD: DeckService.getPersonalizedRecommendations failed after 85ms with error: DataAccessException
2025-07-22 23:09:59.523 [http-nio-8082-exec-3] ERROR PERFORMANCE - 
                [] [98ms] [] FAILED_ENDPOINT: DeckController.getPersonalizedRecommendations failed after 98ms with error: DataAccessException
